import type { PlaywrightTestConfig } from '@playwright/test'
import { devices } from '@playwright/test'

/**
 * Playwright Environment Variables.
 * @see https://github.com/motdotla/dotenv
 *
 * require('dotenv').config();
 */

/**
 * Playwright Config.
 * @see https://playwright.dev/docs/test-configuration.
 */
const config: PlaywrightTestConfig = {
  // Inits global setup.
  // globalSetup: require.resolve('./tests/setup-playwright.ts'),

  // Directory that will be recursively scanned for test files.
  testDir: './tests/e2e',

  // Maximum time one test can run for.
  timeout: 30 * 1000,

  expect: {
    // Maximum time expect() should wait for the condition to be met.
    // For example in `await expect(locator).toHaveText();`
    timeout: 5000,
  },

  // Run tests in files in parallel.
  fullyParallel: true,

  // Fail the build on CI if you accidentally left test.only in the source code.
  forbidOnly: !!process.env.CI,

  // Retry on CI only
  retries: process.env.CI ? 2 : 0,

  // Opt out of parallel tests on CI.
  workers: process.env.CI ? 1 : undefined,

  // Reporter to use. See https://playwright.dev/docs/test-reporters
  reporter: 'html',

  // Shared settings for all the projects below.
  // See https://playwright.dev/docs/api/class-testoptions.
  use: {
    // Tells all tests to load signed-in state from 'my-file.json'.
    // storageState: './tests/auth-storage.json',

    // Maximum time each action such as `click()` can take. Defaults to 0 (no limit).
    actionTimeout: 0,

    // Base URL to use in actions like `await page.goto('/')`.
    baseURL: 'http://localhost:8811/',

    // Collect trace when retrying the failed test.
    // See https://playwright.dev/docs/trace-viewer
    trace: 'on-first-retry',
  },

  // Configure projects for major browsers.
  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
      },
    },
  ],

  // Folder for test artifacts such as screenshots, videos, traces, etc.
  // outputDir: 'test-results/',

  // Run your local dev server before starting the tests.
  webServer: {
    command: 'cross-env PORT=8811 npm run dev',
    port: 8811,
  },
}

export default config
