import { S3Client, ListObjectsV2Command } from "@aws-sdk/client-s3";

export async function fetchS3Images(bucketName: string): Promise<string[]> {
  try {
    console.log(`Attempting to fetch images from bucket: ${bucketName}`);
    console.log(`Environment: ${process.env.NODE_ENV}`);

    // In development, use placeholder images
    if (process.env.NODE_ENV === 'development') {
      console.log('Using development placeholder images');
      const placeholderImages = [
        '/tastavino-132.jpg',
        '/tastavino-117.jpg',
        '/tastavino-112.jpg',
        '/tastavino-77.jpg',
        '/tastavino-68.jpg',
        '/tastavino-66.jpg',
        '/tastavino-57.jpg',
        '/eventphoto.jpeg',
        '/borgo.jpeg',
        '/scop.jpg',
        '/marescopello.jpg',
        '/scop3.jpg',
        '/marescopello1.jpg',
        '/tastavino2.jpg'
      ];

      // Simulate some loading time
      await new Promise(resolve => setTimeout(resolve, 500));

      return placeholderImages;
    }
    
    // In production, use the real S3 images
    const s3 = new S3Client({ region: 'eu-central-1' });
    const command = new ListObjectsV2Command({
      Bucket: bucketName
    });
    
    const data = await s3.send(command);

    console.log(`Fetched ${data.Contents?.length || 0} items from bucket: ${bucketName}`);

    // Filter for image files only
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const imageUrls = data.Contents?.filter(item => {
      const key = item.Key?.toLowerCase() || '';
      return imageExtensions.some(ext => key.endsWith(ext));
    }).map(item => {
      // Use the same CloudFront URL pattern as the working tastavino.galleria.tsx
      const url = `https://d3fcvp7s4vbn7p.cloudfront.net/${item.Key}`;
      console.log(`Generated URL: ${url}`);
      return url;
    }) || [];

    console.log(`Returning ${imageUrls.length} image URLs for bucket: ${bucketName}`);
    return imageUrls;
  } catch (error) {
    console.error(`Error fetching images from S3 bucket ${bucketName}:`, error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      bucketName,
      region: 'eu-central-1'
    });

    // Return empty array on error so the page doesn't break
    return [];
  }
}



export async function fetchTastavinoIIGalleryImages(): Promise<string[]> {
  // Temporarily use the working viediscopello bucket until new buckets are set up
  console.log('Fetching Tastavino II gallery images from viediscopello bucket');
  return fetchS3Images('viediscopello');
}

export async function fetchTastavinoIIMontageImages(): Promise<string[]> {
  // Temporarily use the working viediscopello bucket until new buckets are set up
  console.log('Fetching Tastavino II montage images from viediscopello bucket');
  return fetchS3Images('viediscopello');
}

export async function fetchTastavinoIINightImages(): Promise<string[]> {
  // In development, provide some placeholder night images
  if (process.env.NODE_ENV === 'development') {
    const nightPlaceholderImages = [
      '/tastavino-132.jpg',
      '/tastavino-117.jpg',
      '/borgo.jpeg',
      '/eventphoto.jpeg',
      '/marescopello1.jpg',
      '/tastavino2.jpg'
    ];

    // Simulate some loading time
    await new Promise(resolve => setTimeout(resolve, 300));

    return nightPlaceholderImages;
  }

  // Temporarily use the working viediscopello bucket until new buckets are set up
  console.log('Fetching Tastavino II night images from viediscopello bucket');
  return fetchS3Images('viediscopello');
}
