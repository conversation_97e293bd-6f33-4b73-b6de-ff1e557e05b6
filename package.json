{"name": "viadiscopellotickets", "private": true, "author": "https://github.com/dev-xo", "sideEffects": false, "scripts": {"build": "remix build", "dev": "remix dev", "format": "prettier --write .", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/index.js", "test:e2e:dev": "playwright test", "test:e2e:install": "npx playwright install chromium --with-deps", "pretest:e2e:run": "npm run build", "test:e2e:run": "cross-env CI=true playwright test", "typecheck": "tsc", "validate": "npm run lint && npm run typecheck && npm run test:e2e:run"}, "dependencies": {"@aws-sdk/client-s3": "^3.588.0", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-brands-svg-icons": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@prisma/client": "^6.7.0", "@remix-run/node": "^2.2.0", "@remix-run/react": "^2.2.0", "@remix-run/serve": "^2.2.0", "@stitches/react": "^1.2.8", "@stripe/react-stripe-js": "^2.7.0", "@stripe/stripe-js": "^3.3.0", "autoprefixer": "^10.4.16", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "clsx": "^2.1.0", "dayjs": "^1.11.10", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "intl-parse-accept-language": "^1.0.0", "isbot": "^3.7.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.2", "react-icons": "^5.2.1", "remix-utils": "^7.1.0", "stripe": "^11.18.0", "tiny-invariant": "^1.3.3", "validate-npm-package-name": "^5.0.0"}, "devDependencies": {"@playwright/test": "^1.39.0", "@remix-run/dev": "^2.2.0", "@remix-run/eslint-config": "^2.2.0", "@types/bcryptjs": "^2.4.6", "@types/eslint": "^8.44.6", "@types/react": "^18.2.34", "@types/react-dom": "^18.2.14", "cross-env": "^7.0.3", "eslint": "^8.53.0", "eslint-config-prettier": "^8.10.0", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.3.0", "prisma": "^6.7.0", "remix-flat-routes": "^0.8.5", "stripe-event-types": "^2.4.0", "tailwindcss": "^3.3.5", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}, "prisma": {"seed": "ts-node --require tsconfig-paths/register prisma/seed.ts"}}