{"include": ["remix.env.d.ts", "**/*.ts", "**/*.tsx"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2020"], "target": "ES2020", "jsx": "react-jsx", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "isolatedModules": true, "esModuleInterop": true, "resolveJsonModule": true, "allowJs": true, "noEmit": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"~/*": ["./app/*"]}}}