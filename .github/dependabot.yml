# Learn about Dependabot:
# https://docs.github.com/en/code-security/dependabot

version: 2
updates:
  # Enable version updates for npm.
  - package-ecosystem: 'npm'
    # Look for `package.json` and `lock` files in the `root` directory.
    directory: '/'
    # Check the npm registry for updates every day.
    schedule:
      interval: 'daily'

  # Enable version updates for Github-Actions.
  - package-ecosystem: github-actions
    # Look in the `root` directory.
    directory: /
    # Check for updates every day (weekdays)
    schedule:
      interval: daily
