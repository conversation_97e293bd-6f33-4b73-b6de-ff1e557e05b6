name: 🚀 Deploy

on:
  push:
    branches:
      - main
      - dev
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  actions: write
  contents: read

jobs:
  lint:
    name: ⬣ ESLint
    runs-on: ubuntu-latest
    steps:
      - name: ⬇️ Checkout repo
        uses: actions/checkout@v4

      - name: ⎔ Setup node
        uses: actions/setup-node@v4
        with:
          cache: npm
          cache-dependency-path: ./package.json
          node-version: 18

      - name: 📥 Install deps
        run: npm install

      - name: 🔬 Lint
        run: npm run lint

  typecheck:
    name: ʦ TypeScript
    runs-on: ubuntu-latest
    steps:
      - name: ⬇️ Checkout repo
        uses: actions/checkout@v4

      - name: ⎔ Setup node
        uses: actions/setup-node@v4
        with:
          cache: npm
          cache-dependency-path: ./package.json
          node-version: 18

      - name: 📥 Install deps
        run: npm install

      - name: 🔎 Type check
        run: npm run typecheck --if-present

  playwright:
    name: 🎭 Playwright
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - name: ⬇️ Checkout repo
        uses: actions/checkout@v4

      - name: ⎔ Setup node
        uses: actions/setup-node@v4
        with:
          cache: npm
          cache-dependency-path: ./package.json
          node-version: 18

      - name: 📥 Install deps
        run: npm install

      - name: 🏄 Copy test env vars
        run: cp .env.example .env

      # - name: Install Playwright Browsers
      #   run: npx playwright install --with-deps

      # - name: 🛠 Setup Database
      #   run: npx prisma migrate reset --force --skip-seed

      # - name: ⚙️ Build
      #   run: npm run build

      # - name: Run Playwright Tests
      #   run: npx playwright test

      # - name: Upload Report
      #   uses: actions/upload-artifact@v3
      #   if: always()
      #   with:
      #     name: playwright-report
      #     path: playwright-report/
      #     retention-days: 30

  deploy:
    name: 🚀 Deploy
    runs-on: ubuntu-latest
    needs: [lint, typecheck, playwright]
    # Only build / deploy main/dev branch on pushes.
    if: ${{ (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev') && github.event_name == 'push' }}

    steps:
      - name: ⬇️ Checkout repo
        uses: actions/checkout@v4

      - name: 👀 Read app name
        uses: SebRollen/toml-action@v1.0.2
        id: app_name
        with:
          file: fly.toml
          field: app

      - name: 🎈 Setup Fly
        uses: superfly/flyctl-actions/setup-flyctl@v1.4

      - name: 🚀 Deploy Production
        if: ${{ github.ref == 'refs/heads/main' }}
        run: flyctl deploy --remote-only --build-arg COMMIT_SHA=${{ github.sha }} --app ${{ steps.app_name.outputs.value }}
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
